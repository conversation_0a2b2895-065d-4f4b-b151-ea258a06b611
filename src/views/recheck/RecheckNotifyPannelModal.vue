<template>
  <a-modal
    title="复查管理"
    width="80%"
    :open="visible"
    @ok="handleCancel"
    :okButtonProps="{ class: { 'jee-hidden': true } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div style="height: 75vh; overflow-y: auto">
      <a-row :gutter="8">
        <a-col :span="10">
          <a-card size="small" title="1、请选择复查项目">
            <template #extra>
              <a-space>
                <a-button
                  type="primary"
                  ghost
                  size="small"
                  @click="handleSelectAllAbnormal"
                  :loading="abnormalLoading"
                  :disabled="abnormalGroups.length === 0"
                >
                  <CarryOutOutlined />
                  一键选中异常项目
                </a-button>
                <a-tag color="orange" v-if="abnormalGroups.length > 0">
                  {{ abnormalGroups.length }} 项异常
                </a-tag>
              </a-space>
            </template>
            <!-- 异常项目展示区域 -->
            <a-collapse v-if="abnormalGroups.length > 0" style="margin-bottom: 8px" size="small" :default-active-key="['abnormal']">
              <a-collapse-panel key="abnormal" header="当前异常项目">
                <template #extra>
                  <a-tag color="red">{{ abnormalGroups.length }} 项</a-tag>
                </template>
                <div style="max-height: 150px; overflow-y: auto">
                  <a-spin :spinning="abnormalLoading">
                    <a-list size="small" :data-source="abnormalGroups">
                      <template #renderItem="{ item }">
                        <a-list-item>
                          <a-list-item-meta>
                            <template #title>
                              <a-space>
                                <a-tag color="orange" size="small">{{ item.departName }}</a-tag>
                                <span>{{ item.itemGroupName }}</span>
                                <span v-if="item.checkPartName" style="color: #666;">（{{ item.checkPartName }}）</span>
                              </a-space>
                            </template>
                          </a-list-item-meta>
                          <template #actions>
                            <a @click="handleAddOneAbnormal(item)">添加</a>
                          </template>
                        </a-list-item>
                      </template>
                    </a-list>
                  </a-spin>
                </div>
              </a-collapse-panel>
            </a-collapse>

            <!-- 无异常项目提示 -->
            <a-alert
              v-else-if="!abnormalLoading && customerRegId"
              message="当前体检人员暂无异常项目"
              type="info"
              show-icon
              style="margin-bottom: 8px"
            />

            <a-row :gutter="2">
              <a-col :span="8" style="height: 45vh; overflow: auto">
                <DepartTree ref="leftTree" @select="searchGroupByDepartment" @root-tree-data="onRootTreeData" />
              </a-col>
              <a-col :span="16">
                <a-form layout="inline" :model="searchForm" style="margin-bottom: 10px">
                  <a-form-item labelAlign="left">
                    <a-input-search allow-clear @change="searchGroupByKeyword" size="middle" placeholder="名称或助记码查询" />
                  </a-form-item>
                  <a-button type="primary" @click="handelSelected">批量添加<ArrowRightOutlined /></a-button>
                </a-form>
                <a-table
                  :loading="groupLoading"
                  :bordered="false"
                  :scroll="{ y: '45vh' }"
                  :pagination="false"
                  row-key="id"
                  :columns="groupColumns"
                  :data-source="groupDatasource"
                  size="small"
                  :row-selection="{ selectedRowKeys: groupTableState.selectedRowKeys, onChange: onGroupTableSelectChange }"
                >
                  <template #bodyCell="{ text, record, index, column }">
                    <template v-if="column.dataIndex === 'operation'">
                      <!--                      <a @click="showItem(record)">项目</a>
                        <a-divider type="vertical" />-->
                      <a @click="handleAddOne(record)">添加</a>
                    </template>
                  </template>
                </a-table>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
        <a-col :span="14">
          <a-card size="small" title="2、填写复查信息">
            <template #extra>
              <a-button type="primary" @click="handleSave"><SaveOutlined />保存</a-button>
            </template>
            <RecheckNotifyForm
              ref="registerForm"
              @ok="submitCallback"
              :formDisabled="disableSubmit"
              :formBpm="false"
              :customer-reg-id="customerRegId"
              :summary-id="summaryId"
            />
          </a-card>
          <a-card size="small" title="复查列表" style="margin-top: 8px">
            <RecheckNotifyList ref="registerList" @update-total="handleUpdateTotal" :customer-reg-id="customerRegId" :summary-id="summaryId" />
          </a-card>
        </a-col>
      </a-row>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, defineExpose, nextTick, reactive, ref } from 'vue';
  import RecheckNotifyForm from '@/views/recheck/components/RecheckNotifyForm.vue';
  import RecheckNotifyList from '@/views/recheck/RecheckNotifyList.vue';
  import DepartTree from '@/views/basicinfo/components/DepartTree.vue';
  import { ArrowRightOutlined, SaveOutlined, CarryOutOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { Group, Key } from '#/types';
  import type { TableColumnType } from 'ant-design-vue';
  import { getAllGroup } from '@/views/basicinfo/ItemGroup.api';
  import { listDepartGroupByReg } from '@/views/summary/Summary.api';

  const registerForm = ref();
  const registerList = ref();

  const width = ref<string>('80%');
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const emit = defineEmits(['register', 'success', 'update-total']);
  const { createMessage } = useMessage();
  const customerRegId = ref();
  const summaryId = ref();

  /**异常项目相关*/
  const abnormalGroups = ref([]);
  const abnormalLoading = ref(false);

  /**项目组合列表*/
  const rootTreeData = ref<any[]>([]);
  const groupList = ref();
  const groupListInited = ref<boolean>(false);
  // 左侧树选择后触发

  // 左侧树rootTreeData触发
  function onRootTreeData(data) {
    rootTreeData.value = data;
  }

  const selectedDepartId = ref<string | null>(null);
  const searchForm = reactive({});
  const groupLoading = ref<boolean>(false);
  const groupDatasource = ref<Group[]>([]);
  const groupTableState = reactive<{
    selectedRowKeys: Key[];
    selectedRows: Group[];
    loading: boolean;
    filteredDepart: string | null;
  }>({
    selectedRowKeys: [], // Check here to configure the default column
    selectedRows: [],
    loading: false,
    filteredDepart: null,
  });

  const groupColumns = computed<TableColumnType[]>(() => {
    return [
      {
        title: '组合名称',
        dataIndex: 'name',
        width: '40%',
      },
      {
        title: '科室',
        dataIndex: 'departmentName',
        width: '30%',
        ellipsis: true,
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '15%',
      },
    ];
  });

  const fetchGroup = () => {
    if (!groupListInited.value) {
      groupLoading.value = true;
      getAllGroup({})
        .then((res) => {
          groupList.value = res;
          groupDatasource.value = res;
          groupListInited.value = true;
        })
        .finally(() => {
          groupLoading.value = false;
        });
    }
  };
  const searchGroupByDepartment = (depart) => {
    if (depart.isLeaf) {
      selectedDepartId.value = depart.id;
      groupDatasource.value = groupList.value.filter((item) => {
        return item.departmentId == depart.id;
      });
    } else {
      selectedDepartId.value = '';
      groupDatasource.value = groupList.value;
    }
  };
  const searchGroupByKeyword = (e) => {
    let keyword = e.target.value;
    if (keyword) {
      groupDatasource.value = groupList.value.filter((item) => {
        let departMatched = true;
        if (selectedDepartId.value) {
          departMatched = item.departmentId == selectedDepartId.value;
        }

        let wordMatched = item.name.includes(keyword) || item.helpChar?.toLowerCase().includes(keyword?.toLowerCase());
        if (wordMatched && departMatched) {
          return true;
        }
        return false;
      });
    } else {
      if (selectedDepartId.value) {
        groupDatasource.value = groupList.value.filter((item) => {
          return item.departmentId == selectedDepartId.value;
        });
      } else {
        groupDatasource.value = groupList.value;
      }
    }
  };
  const onGroupTableSelectChange = (selectedRowKeys: Key[], selectedRows: Group[]) => {
    groupTableState.selectedRowKeys = selectedRowKeys;
    groupTableState.selectedRows = selectedRows;
  };

  function handelSelected() {
    if (groupTableState.selectedRows.length == 0) {
      createMessage.error('请选择复查项目');
    }

    let selectedItemGroups = groupTableState.selectedRows.map((item) => {
      return {
        name: item.name,
        id: item.id,
      };
    });
    registerForm.value.add(selectedItemGroups);
  }
  function handleAddOne(record) {
    registerForm.value.add([{ name: record.name, id: record.id }]);
  }

  function handleUpdateTotal(total) {
    emit('update-total', total);
  }

  /**
   * 获取异常项目
   */
  async function fetchAbnormalGroups() {
    if (!customerRegId.value) {
      abnormalGroups.value = [];
      return;
    }

    abnormalLoading.value = true;
    try {
      const data = await listDepartGroupByReg({ regId: customerRegId.value });
      const abnormalGroupsTemp = [];

      data.forEach((departGroupBean) => {
        departGroupBean.groupList.forEach((group) => {
          // 检查是否有异常的小项
          const hasAbnormalItems = group.resultList && group.resultList.some(item => item.abnormalFlag === '1');
          if (hasAbnormalItems) {
            abnormalGroupsTemp.push({
              id: group.itemGroupId,
              name: group.itemGroupName,
              itemGroupId: group.itemGroupId,
              itemGroupName: group.itemGroupName,
              checkPartName: group.checkPartName,
              departName: departGroupBean.depart.departName
            });
          }
        });
      });

      abnormalGroups.value = abnormalGroupsTemp;
    } catch (error) {
      console.error('获取异常项目失败:', error);
      createMessage.error('获取异常项目失败');
      abnormalGroups.value = [];
    } finally {
      abnormalLoading.value = false;
    }
  }

  /**
   * 一键选中所有异常项目
   */
  function handleSelectAllAbnormal() {
    if (abnormalGroups.value.length === 0) {
      createMessage.warning('当前没有异常项目');
      return;
    }

    const selectedItemGroups = abnormalGroups.value.map((item) => ({
      name: item.name,
      id: item.id,
    }));

    registerForm.value.add(selectedItemGroups);
    createMessage.success(`已添加 ${selectedItemGroups.length} 个异常项目到复查列表`);
  }

  /**
   * 添加单个异常项目
   */
  function handleAddOneAbnormal(record) {
    registerForm.value.add([{ name: record.name, id: record.id }]);
    createMessage.success(`已添加"${record.name}"到复查列表`);
  }

  /**
   * 打开
   */
  function open(customerRegIdParam, summaryIdParam) {
    customerRegId.value = customerRegIdParam;
    summaryId.value = summaryIdParam;
    visible.value = true;
    nextTick(() => {
      fetchGroup();
      fetchAbnormalGroups(); // 获取异常项目
    });
  }

  /**
   * 确定按钮点击事件
   */
  function handleSave() {
    registerForm.value.submitForm();
  }

  /**
   * form保存回调事件
   */
  function submitCallback() {
    registerList.value?.reloadPage();
    emit('success');
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
    // 清理数据
    abnormalGroups.value = [];
    customerRegId.value = null;
    summaryId.value = null;
  }

  defineExpose({
    open,
    disableSubmit,
  });
</script>

<style>
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }
</style>
