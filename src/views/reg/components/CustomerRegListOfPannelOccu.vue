<template>
  <div>
    <a-card size="small" style="padding: 0; height: 95vh" title="职业病体检登记列表">
      <template #extra>
        <a-button type="primary" size="middle" @click="handleAdd">新增职业病体检登记</a-button>
      </template>
      <!--查询区域-->
      <div class="jeecg-basic-table-form-container">
        <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row :gutter="[4, 8]">
            <a-col :span="12">
              <a-input
                allow-clear
                @focus="readIdCard4RegList"
                size="middle"
                :placeholder="idcDataSource.action == 'searchRegList' ? idcData.msg : '身份证号'"
                v-model:value="queryParam.idCard"
              />
            </a-col>
            <a-col :span="12">
              <a-input allow-clear size="middle" placeholder="体检号" v-model:value="queryParam.examNo" />
            </a-col>
            <a-col :span="12">
              <a-input allow-clear size="middle" placeholder="姓名" v-model:value="queryParam.name" />
            </a-col>
            <a-col :span="12">
              <j-dict-select-tag dict-code="regStatus" size="middle" placeholder="登记状态" v-model:value="queryParam.status" style="width: 100%" />
            </a-col>
            <a-col :span="24">
              <a-select style="width: 20%" v-model:value="queryParam.dateType">
                <a-select-option value="登记">登记</a-select-option>
                <a-select-option value="预约">预约</a-select-option>
                <a-select-option value="添加">添加</a-select-option>
              </a-select>
              <a-range-picker
                v-model:value="regDateRange"
                placement="登记日期"
                style="width: 80%"
                @change="searchQuery"
                :presets="rangePresets"
                :allow-clear="false"
              />
            </a-col>
            <template v-if="toggleSearchStatus">
              <a-col :span="12">
                <j-select-user size="middle" placeholder="登记员工" v-model:value="queryParam.creatorBy" style="width: 100%" />
              </a-col>
              <a-col :span="12">
                <a-select size="middle" placeholder="交表状态" v-model:value="queryParam.retrieveStatus" style="width: 100%">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option value="0">未交表</a-select-option>
                  <a-select-option value="1">已交表</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="12">
                <j-async-search-select
                  size="middle"
                  placeholder="所属预约"
                  @change="getTeamList"
                  v-model:value="queryParam.companyRegId"
                  dict="company_reg where lock_status=0 ,reg_name,id"
                  allowClear
                  style="width: 100%"
                />
              </a-col>
              <a-col :span="12">
                <a-select size="middle" placeholder="所属分组" v-model:value="queryParam.teamId" allowClear style="width: 100%">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option :value="item.id" v-for="item in teamList">{{ item.name }}</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="12">
                <a-input allow-clear size="middle" placeholder="单位名称" v-model:value="queryParam.companyName" />
              </a-col>
              <a-col :span="12">
                <j-async-search-select
                  size="middle"
                  placeholder="危害因素"
                  v-model:value="queryParam.riskFactor"
                  dict="zy_risk_factor,name,code"
                  :multiple="true"
                  allowClear
                  style="width: 100%"
                />
              </a-col>
            </template>
          </a-row>

          <a-row :gutter="12" style="margin-top: 10px">
            <a-col :xl="12" :span="12" :md="12" :sm="24">
              <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                <a-button size="small" type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button size="small" type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px"
                  >重置</a-button
                >
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!--引用表格-->
      <BasicTable @register="registerTable" :rowSelection="rowSelection">
        <!--插槽:table标题-->
        <template #tableTitle>
          <template v-if="selectedRowKeys.length > 0">
            <a-space>
              <a-button type="primary" size="small" @click="batchReg" :loading="actionLoading" :disabled="actionLoading">登记</a-button>
              <a-button
                type="primary"
                size="small"
                @click="sendFee"
                v-if="hasPermission('reg:customer_reg:sendFee2HisBatch')"
                :loading="actionLoading"
                :disabled="actionLoading"
                >发送收费申请</a-button
              >
              <a-popover trigger="hover">
                <template #content>
                  <a-checkbox-group v-model:value="batchPrintContent" :options="['导引单', '申请单', '条码', '体检号']" />
                  <a-button type="primary" @click="handleBatchPrint">打印</a-button>
                </template>
                <a-button type="dashed" size="small">打印</a-button>
              </a-popover>

              <a-button
                type="primary"
                size="small"
                @click="batchHandleDelete"
                v-if="hasPermission('reg:customer_reg:deleteBatch')"
                danger
                :loading="actionLoading"
                :disabled="actionLoading"
                >删除</a-button
              >
            </a-space>
          </template>
        </template>

        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex == 'serialNo'"> {{ record.serialNo || '-' }} | {{ record.appointmentSort }} </template>
          <template v-if="column.dataIndex == 'status'">
            <a-tag color="green" v-if="text === '已登记'"> {{ text }}</a-tag>
            <a-tag color="red" v-else-if="text === '未登记'"> {{ text }}</a-tag>
            <a-tag v-else> {{ text }}</a-tag>
          </template>
          <template v-if="column.dataIndex == 'examCategory'">
            <a-tag color="orange">职业病体检</a-tag>
          </template>
        </template>
      </BasicTable>
    </a-card>
  </div>
</template>

<script lang="ts" name="CustomerRegListOfPannelOccu" setup>
  import { computed, inject, reactive, ref, watch } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columnsLite } from './CustomerReg.data';
  import { batchDelete, list, regBatch, sendFee2HisByIds } from './CustomerReg.api';
  import { companyTeamList } from './CompanyReg.api';
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { message, theme } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { JAsyncSearchSelect } from '@/components/Form';
  import JSelectUser from '@/components/Form/src/jeecg/components/JSelectUser.vue';
  import dayjs from 'dayjs';
  import type { RangeValue } from '#/types';
  import { usePermission } from '@/hooks/web/usePermission';
  import { IdcData } from '#/utils';
  import { Icon } from '@/components/Icon';

  const { hasPermission } = usePermission();

  const { createConfirm, notification, createErrorModal } = useMessage();
  const emit = defineEmits(['rowClick', 'readIdcard', 'add', 'batchRegOk', 'batchPrint']);
  const formRef = ref();
  // 固定查询条件中的体检分类为职业病体检
  const queryParam = reactive<any>({ 
    dateType: '预约',
    examCategory: '职业病体检'  // 固定为职业病体检
  });
  const regDateRange = ref<RangeValue>([dayjs(), dayjs()]);

  const toggleSearchStatus = ref<boolean>(false);
  const userStore = useUserStore();
  const { token } = theme.useToken();
  const actionLoading = ref(false);

  /**接收并监听身份证信息，据此进行查询*/
  const idcData = inject<IdcData>('idCardDataKey', {
    data: {},
    ok: false,
    msg: '',
    state: '',
    action: '',
  });
  const sendIdCardCmd = inject('idCardSendMethod', (cmd) => {});
  const idcDataSource = computed(() => ({
    data: idcData.data,
    action: idcData.action,
  }));
  watch(idcDataSource, (val) => {
    if (val && val.data.idCardNo && val.action == 'searchRegList') {
      const idCardNo = val.data.idCardNo; // Store the value in a temporary variable
      queryParam.idCard = idCardNo; // Assign the value to queryParam.idCard
      searchQuery();
    }
  });
  function readIdCard4RegList() {
    idcData.data = {};
    idcData.action = 'searchRegList';
    sendIdCardCmd('ReadStart');
  }
  function readIdCard4RegAdd() {
    idcData.data = {};
    idcData.action = 'addReg';
    sendIdCardCmd('ReadStart');
  }

  const rangePresets = ref([
    { label: '今天', value: [dayjs(), dayjs()] },
    { label: '昨天', value: [dayjs().add(-1, 'd'), dayjs().add(-1, 'd')] },
    { label: '过去一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '过去二周', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '过去30天', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '过去90天', value: [dayjs().add(-90, 'd'), dayjs()] },
    { label: '过去一年', value: [dayjs().add(-1, 'y'), dayjs()] },
    { label: '过去两年', value: [dayjs().add(-2, 'y'), dayjs()] },
    { label: '过去二十年', value: [dayjs().add(-20, 'y'), dayjs()] },
  ]);

  /**表格相关操作*/

  const currentRow = ref<any>({});
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      showIndexColumn: false,
      showTableSetting: true,
      api: list,
      columns: columnsLite,
      canResize: true,
      canColDrag: true,
      showHeader: true,
      useSearchForm: false,
      clickToRowSelect: false,
      size: 'small',
      showActionColumn: false,
      scroll: { y: '50vh' },
      striped: true,
      customRow: (record) => {
        return {
          onClick: () => {
            currentRow.value = record;
            emit('rowClick', record);
          },
        };
      },
      rowClassName: (record) => {
        return currentRow.value && currentRow.value.id === record.id ? 'row-selected' : '';
      },
      beforeFetch: (params) => {
        if (regDateRange.value && regDateRange.value.length == 2) {
          queryParam.regTimeStart = regDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00';
          queryParam.regTimeEnd = regDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59';
        }
        // 确保查询参数中包含职业病体检的过滤条件
        return Object.assign(params, queryParam);
      },
    },
  });
  const [
    registerTable,
    { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource },
    { rowSelection, selectedRowKeys, selectedRows },
  ] = tableContext;

  const teamList = ref<any[]>([]);
  const labelCol = reactive({
    xs: 0,
    sm: 0,
    xl: 0,
    xxl: 0,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 24,
    xl: 24,
    xxl: 24,
  });

  function getTeamList(companyRegId) {
    if (!companyRegId) {
      teamList.value = [];
      queryParam.teamId = '';
      return;
    }
    teamList.value = [];
    queryParam.teamId = '';
    companyTeamList({ companyRegId: companyRegId, pageSize: 10000 }).then((res) => {
      teamList.value = res.records;
    });
  }

  /**打印*/
  const batchPrintContent = ref(['导引单']);
  function handleBatchPrint() {
    if (selectedRows.value.length > 0) {
      let validArr = selectedRows.value.filter((item) => item.status == '已登记');
      if (validArr.length == 0) {
        message.error('没有符合条件的数据！');
        return;
      }
      emit('batchPrint', { regList: selectedRows.value, printTasks: batchPrintContent.value });
    } else {
      message.error('未选择任何数据！');
    }
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    emit('add');
    readIdCard4RegAdd();
  }

  function batchReg(sendFee2His: string = '0') {
    if (selectedRows.value.length > 0) {
      let validArr = selectedRows.value.filter((item) => item.status != '已登记');
      if (validArr.length == 0) {
        message.error('没有符合条件的数据！');
        return;
      }
      createConfirm({
        iconType: 'warning',
        title: '批量登记确认',
        content: `${validArr.length}(共选中${selectedRows.value.length})条符合条件，确认批量登记？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          let validArr = selectedRows.value.filter((item) => item.status == '未登记' || !item.status);
          let info = {
            sendFee2His: sendFee2His,
            ids: validArr.map((item) => item.id),
          };

          actionLoading.value = true;
          regBatch(info)
            .then((res) => {
              if (res.success) {
                let batchRes = res.result;

                let msg = '';
                if (batchRes.failureCount) {
                  let failReason = batchRes.failureResults.map((item) => {
                    return `${item.item?.name}:${item.reason}`;
                  });

                  msg = `成功${batchRes.successCount}条，失败${batchRes.failureCount}条。<br/>失败原因：${failReason.join('<br/>')}`;
                  createErrorModal({
                    title: `批量登记结果`,
                    content: `<div style="max-height: 50vh;overflow-y: auto;">${msg}</div>`,
                  });
                } else {
                  msg = `成功${batchRes.successCount}条。`;
                  message.success(msg);
                }

                if (batchRes.successCount > 0) {
                  emit('batchRegOk', batchRes.successResults);
                }
                reload();
                selectedRowKeys.value = [];
                selectedRows.value = [];
              } else {
                message.error('登记失败');
              }
            })
            .finally(() => {
              actionLoading.value = false;
            });
        },
      });
    } else {
      message.error('登记信息为空！');
    }
  }

  function sendFee() {
    if (selectedRows.value.length > 0) {
      let validArr = selectedRows.value.filter((item) => item.paymentState != '已支付' && item.status == '已登记');
      if (validArr.length == 0) {
        message.error('没有符合条件的数据！');
        return;
      }

      createConfirm({
        iconType: 'warning',
        title: '发送收费申请',
        content: `${validArr.length}(共选中${selectedRows.value.length})条符合条件，确认批量发送收费申请？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          let info = {
            ids: validArr.map((item) => item.id),
          };

          actionLoading.value = true;
          sendFee2HisByIds(info)
            .then((res) => {
              if (res.success) {
                let batchRes = res.result;

                let msg = '';
                if (batchRes.failureCount) {
                  let failReason = batchRes.failureResults.map((item) => {
                    return `${item.item?.name}:${item.reason}`;
                  });

                  msg = `成功${batchRes.successCount}条，失败${batchRes.failureCount}条。<br/>失败原因：${failReason.join('<br/>')}`;
                  createErrorModal({
                    title: `发送收费申请结果`,
                    content: `<div style="max-height: 50vh;overflow-y: auto;">${msg}</div>`,
                  });
                } else {
                  msg = `成功${batchRes.successCount}条。`;
                  message.success(msg);
                }

                reload();
                selectedRowKeys.value = [];
                selectedRows.value = [];
              } else {
                message.error('发送失败');
              }
            })
            .finally(() => {
              actionLoading.value = false;
            });
        },
      });
    } else {
      message.error('登记信息为空！');
    }
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    if (selectedRowKeys.value.length === 0) {
      message.error('未选择任何数据！');
      return;
    }
    actionLoading.value = true;
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
    actionLoading.value = false;
    //如果currentRow被删除，清空currentRow
    if (selectedRowKeys.value.includes(currentRow.value.id)) {
      currentRow.value = {};
      emit('rowClick', {});
    }
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 查询
   */
  async function searchQuery() {
    await reload();
    //如果只有一条数据，自动选中，并发送selectChange
    let dataSource = getDataSource();
    if (dataSource.length === 1) {
      currentRow.value = dataSource[0];
      emit('rowClick', dataSource[0]);
    } else if (dataSource.length === 0) {
      //emit('selectChange', {});
    }
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    // 重置时保持职业病体检的过滤条件
    queryParam.examCategory = '职业病体检';
    //刷新数据
    reload();
  }

  function reset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    // 重置时保持职业病体检的过滤条件
    queryParam.examCategory = '职业病体检';
    //将时间范围设置为今天
    regDateRange.value = [dayjs(), dayjs()];
    //刷新数据
    reload();
  }

  function reloadPage() {
    reload();
    selectedRows.value = [];
    selectedRowKeys.value = [];
  }

  async function reloadAndSelect(value) {
    await reload();
    if (value && value.id) {
      let dataSource = getDataSource();
      currentRow.value = dataSource.find((item) => item.id === value.id) || {};
      emit('rowClick', currentRow.value);
    }
  }

  defineExpose({
    searchQuery,
    reloadPage,
    reloadAndSelect,
    reset,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 12px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }

  :deep(.row-selected td:first-child) {
    border-left: solid 5px v-bind('token.colorPrimary');
  }
</style>
