<template>
  <a-spin :spinning="groupLoading">
    <a-tabs v-model:activeKey="resultCondition" size="small">
      <a-tab-pane key="all" tab="全部">
        <a-collapse v-model:activeKey="activeKey" size="small" style="padding: 0; margin-top: 4px">
          <a-collapse-panel :key="dIndex" :show-arrow="true" v-for="(depart, dIndex) in departGroupList">
            <template #header>
              <a-typography-text>{{ depart.depart.departName }} </a-typography-text>
            </template>
            <div class="depart-summary">
              小结：<a-typography-text>{{ depart.departSummary?.characterSummary }}</a-typography-text>
            </div>

            <template v-for="(group, gIndex) in depart.groupList">
              <a-table :columns="groupColumns" :data-source="group.resultList" size="small" :show-header="false" :pagination="false">
                <template #title>
                  <div>
                    <a-flex align="center">
                      <a-typography-text strong ellipsis
                        >{{ group.itemGroupName }}{{ group.checkPartName ? `（${group.checkPartName}）` : '' }}
                      </a-typography-text>
                      <a-divider type="vertical" />
                      <a-tag :color="group.checkStatusColor">{{ group.checkStatus }} </a-tag>
                      <a-tag color="red" v-if="group.abandonFlag == 1">已放弃</a-tag>
                      <a-divider type="vertical" />
                      <a-typography-text>接口状态： </a-typography-text><a-tag>{{ group.interfaceSyncStatus || '未知' }}</a-tag>
                      <a-button
                        size="small"
                        type="link"
                        @click="priewPic(group.reportPics || [])"
                        v-if="group.reportPics && group.reportPics.length > 0"
                      >
                        <FileImageOutlined />
                      </a-button>
                    </a-flex>
                    <a-flex align="left">
                      <a-typography-text type="secondary">{{ group.reportDoctorName }}</a-typography-text>
                      <a-divider type="vertical" />
                      <a-typography-text type="secondary">{{ group.checkTime }}</a-typography-text>
                    </a-flex>
                  </div>
                </template>
                <template #bodyCell="{ column, record, text }">
                  <template v-if="column.dataIndex === 'itemName'">
                    <a-flex align="center">
                      <template v-if="record.checkStatus == '未检'">
                        <a-typography-text type="danger">未检</a-typography-text>
                      </template>
                      <template v-else>
                        <div style="display: flex; flex-direction: column; justify-content: space-between">
                          <template v-if="record.abnormalFlag">
                            <template v-if="record.abnormalFlag == '0'">
                              <a-tag color="green" v-if="record.abnormalFlagDesc" style="margin-bottom: 4px">
                                {{ record.abnormalFlagDesc }}
                              </a-tag>
                            </template>
                            <template v-else>
                              <a-tag color="red" style="margin-bottom: 4px"
                                >{{ record.abnormalSymbol || '' + ' ' }}{{ record.abnormalFlagDesc || '异常' }}
                              </a-tag>
                            </template>
                          </template>
                          <template v-if="record.criticalFlag">
                            <a-tag color="red" style="margin-bottom: 4px"
                              >危急值{{ record.criticalDegree ? ':' + record.criticalDegree : '' }}
                            </a-tag>
                          </template>
                        </div>
                      </template>
                      {{ text }}{{ group.checkPartName ? `（${group.checkPartName}）` : '' }}
                    </a-flex>
                  </template>
                  <template v-if="column.dataIndex === 'itemResult'">
                    <div style="display: flex; flex-direction: column">
                      <div>
                        <a-typography-text :type="record.abnormalFlag == '1' ? 'danger' : 'info'"> {{ record.value }}</a-typography-text>
                        <template v-if="record.checkObservations || (record.checkConclusion && group.classCode !== '检验')">
                          <a-divider type="vertical" /> <ReadOutlined class="icon-color" @click="showDetail(record)"
                        /></template>
                        <!--                        <span style="margin-right: 3px; font-weight: bold">{{ record.value }}</span>-->
                        <span>{{ record.unit || '' }}</span>
                        <span v-if="record.valueRefRange" style="margin-left: 4px">{{
                          record.valueRefRange ? '[' + record.valueRefRange + ']' : ''
                        }}</span>
                      </div>
                    </div>
                  </template>
                  <template v-if="column.dataIndex === 'abnormalFlag'">
                    <!--                              <a-tag color="red" v-if="record.abandonFlag == 1" style="margin-left: 3px">已放弃</a-tag>-->
                  </template>
                </template>
              </a-table>
            </template>
          </a-collapse-panel>
        </a-collapse>
      </a-tab-pane>
      <a-tab-pane key="abnormal">
        <template #tab>
          <span style="margin-right: 4px">异常</span>
          <span :style="{ color: token.colorError }">{{ statistics.abnormalCount }}</span>
        </template>
        <a-flex justify="space-between" align="center" style="margin-bottom: 8px" v-if="recheck">
          <a-space>
            <a-typography-text strong>异常项目复查选择</a-typography-text>
            <a-tag color="orange" v-if="selectedAbnormalGroups.length > 0">
              已选择 {{ selectedAbnormalGroups.length }} 项
            </a-tag>
          </a-space>
          <a-space>
            <a-button
              type="primary"
              size="small"
              @click="handleSetRecheck"
              :disabled="selectedAbnormalGroups.length === 0"
            >
              设置为复查项目
            </a-button>
            <a-checkbox
              :checked="isAllAbnormalSelected"
              :indeterminate="isIndeterminate"
              @change="handleSelectAllAbnormal"
            >
              全选异常项目
            </a-checkbox>
          </a-space>
        </a-flex>

        <template v-for="depart in departGroupList">
          <template v-for="(group, gIndex) in depart.groupList" :key="gIndex">
            <a-table
              v-if="group.resultList && group.resultList.length > 0"
              :columns="groupColumns"
              :data-source="group.resultList"
              size="small"
              :show-header="false"
              :pagination="false"
            >
              <template #title>
                <div>
                  <div style="display: flex; justify-content: space-between; align-items: center">
                    <div>
                      <a-typography-text strong ellipsis
                        >{{ group.itemGroupName }} {{ group.checkPartName ? `（${group.checkPartName}）` : '' }}</a-typography-text
                      >
                      <!--                      <a-divider type="vertical" />
                      <a-tag :color="group.checkStatusColor">{{ group.checkStatus }} </a-tag>
                      <a-tag color="red" v-if="group.abandonFlag == 1">已放弃</a-tag>-->
                      <a-divider type="vertical" />
                      <a-button
                        size="small"
                        type="link"
                        @click="priewPic(group.reportPics || [])"
                        v-if="group.reportPics && group.reportPics.length > 0"
                      >
                        <FileImageOutlined />
                      </a-button>
                    </div>
                    <div>
                      <a-checkbox
                        :checked="selectedAbnormalGroups.includes(group.itemGroupId)"
                        @change="(e) => handleAbnormalGroupSelect(group.itemGroupId, e.target.checked)"
                        style="margin-left: 8px"
                        v-if="recheck"
                      />
                    </div>
                  </div>
                  <a-flex align="left">
                    <a-typography-text type="secondary">{{ group.reportDoctorName }}</a-typography-text>
                    <a-divider type="vertical" />
                    <a-typography-text type="secondary">{{ group.checkTime }}</a-typography-text>
                  </a-flex>
                </div>
              </template>
              <template #bodyCell="{ column, record, text }">
                <template v-if="column.dataIndex === 'itemName'">
                  <a-flex align="center">
                    <template v-if="record.checkStatus == '未检'">
                      <a-typography-text type="danger">未检</a-typography-text>
                    </template>
                    <template v-else>
                      <div style="display: flex; flex-direction: column; justify-content: space-between">
                        <template v-if="record.abnormalFlag">
                          <template v-if="record.abnormalFlag == '0'">
                            <a-tag color="green" v-if="record.abnormalFlagDesc" style="margin-bottom: 4px">
                              {{ record.abnormalFlagDesc }}
                            </a-tag>
                          </template>
                          <template v-else>
                            <a-tag color="red" style="margin-bottom: 4px"
                              >{{ record.abnormalSymbol || '' + ' ' }}{{ record.abnormalFlagDesc || '异常' }}
                            </a-tag>
                          </template>
                        </template>
                        <template v-if="record.criticalFlag">
                          <a-tag color="red" style="margin-bottom: 4px">危急值{{ record.criticalDegree ? ':' + record.criticalDegree : '' }} </a-tag>
                        </template>
                      </div>
                    </template>
                    {{ text }}{{ group.checkPartName ? `（${group.checkPartName}）` : '' }}
                  </a-flex>
                </template>
                <template v-if="column.dataIndex === 'itemResult'">
                  <div style="display: flex; flex-direction: column">
                    <div>
                      <a-typography-text :type="record.abnormalFlag == '1' ? 'danger' : 'info'"> {{ record.value }}</a-typography-text>
                      <template v-if="record.checkObservations || (record.checkConclusion && group.classCode !== '检验')"
                        ><a-divider type="vertical" /> <ReadOutlined class="icon-color" @click="showDetail(record)"
                      /></template>
                      <!--                        <span style="margin-right: 3px; font-weight: bold">{{ record.value }}</span>-->
                      <span>{{ record.unit || '' }}</span>
                      <span v-if="record.valueRefRange" style="margin-left: 4px">{{
                        record.valueRefRange ? '[' + record.valueRefRange + ']' : ''
                      }}</span>
                    </div>
                  </div>
                </template>
              </template>
            </a-table>
          </template>
        </template>
      </a-tab-pane>
      <a-tab-pane key="uncheck" forceRender>
        <template #tab>
          <span style="margin-right: 4px">未检</span>
          <span :style="{ color: token.colorError }">{{ statistics.uncheckCount }}</span>
        </template>
        <a-table :columns="groupColumns2" :data-source="filteredResultList('uncheck')" size="small" :pagination="false">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'itemGroupName'">
              <a-typography-text> {{ text }}</a-typography-text>
            </template>
            <template v-if="column.dataIndex === 'checkStatus'">
              <a-typography-text type="danger" strong>{{ text }}</a-typography-text>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="abandon" tab="放弃" forceRender>
        <template #tab>
          <span style="margin-right: 4px">放弃</span>
          <span :style="{ color: token.colorError }">{{ statistics.abandonCount }}</span>
        </template>
        <a-table :columns="groupColumns2" :data-source="filteredResultList('abandon')" size="small" :pagination="false">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'itemGroupName'">
              <a-typography-text> {{ text }}</a-typography-text>
            </template>
            <template v-if="column.dataIndex === 'checkStatus'">
              <a-typography-text type="danger" strong>已放弃</a-typography-text>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
    <a-modal v-model:open="resultDetailVisiable" width="100%" title="报告详情" :footer="null" wrap-class-name="full-modal">
      <div style="height: 95vh; overflow-y: scroll; padding: 5px">
        <a-row :gutter="12">
          <a-col :span="10">
            <div
              style="height: 90vh; display: flex; justify-content: center; align-items: center; border: #0b3546 dashed 1px"
              v-if="currentRecord?.pic?.length > 0"
            >
              <a-image-preview-group>
                <a-image
                  v-for="(pic, picIndex) in currentRecord.pic"
                  :key="picIndex"
                  :src="getFileAccessHttpUrl(pic)"
                  fallback="data:image/png;base64,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"
                />
              </a-image-preview-group>
            </div>
            <div style="height: 90vh; display: flex; justify-content: center; align-items: center; border: #0b3546 dashed 1px" v-else>
              <a-empty description="暂无图片" />
            </div>
          </a-col>
          <a-col :span="14">
            <a-typography-title :level="5">检查目的</a-typography-title>
            <a-typography-text>{{ currentRecord.checkPurpose ?? '无' }}</a-typography-text>
            <a-typography-title :level="5">检查部位</a-typography-title>
            <a-typography-text>{{ currentRecord.checkParts ?? '无' }}</a-typography-text>
            <a-typography-title :level="5">检查所见</a-typography-title>
            <a-typography-text>{{ currentRecord.checkObservations ?? '无' }}</a-typography-text>
            <a-typography-title :level="5">检查结论</a-typography-title>
            <a-typography-text>{{ currentRecord.checkConclusion ?? '无' }}</a-typography-text>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </a-spin>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { theme } from 'ant-design-vue';
  import { preview } from 'vue3-image-preview';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  import { FileImageOutlined } from '@ant-design/icons-vue';
  import { listDepartGroupByReg } from '@/views/summary/Summary.api';
  import { ReadOutlined } from '@ant-design/icons-vue';
  import { ICustomerRegItemResult } from '#/types';
  import { useMessage } from '@/hooks/web/useMessage';

  const { token } = theme.useToken();
  const { useToken } = theme;
  const emit = defineEmits(['loadData', 'abnormalGroupsSelected']);
  const props = defineProps({
    recheck: {
      type: Boolean,
      default: false,
    },
  });

  const customerRegId = ref('');
  const resultCondition = ref('all');
  const groupLoading = ref(false);
  const activeKey = ref([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]);
  const groupColumns = [
    {
      title: '项目名称',
      dataIndex: 'itemName',
      key: 'itemName',
      width: 120,
    },
    {
      title: '结果',
      dataIndex: 'itemResult',
      key: 'itemResult',
      width: 140,
    },
  ];
  const groupColumns2 = [
    {
      title: '项目名称',
      dataIndex: 'itemGroupName',
      key: 'itemGroupName',
      width: 160,
    },
    {
      title: '检查部位',
      dataIndex: 'checkPartName',
      key: 'checkPartName',
      width: 80,
    },
    {
      title: '检查状态',
      dataIndex: 'checkStatus',
      key: 'checkStatus',
      width: 140,
    },
  ];

  /**复查项目*/
  const recheckItems = ref([]);

  /**异常项目选择相关*/
  const selectedAbnormalGroups = ref([]);

  // 计算属性：是否全选异常项目
  const isAllAbnormalSelected = computed(() => {
    const abnormalGroups = getAbnormalGroups();
    return abnormalGroups.length > 0 && selectedAbnormalGroups.value.length === abnormalGroups.length;
  });

  // 计算属性：是否为半选状态
  const isIndeterminate = computed(() => {
    const abnormalGroups = getAbnormalGroups();
    return selectedAbnormalGroups.value.length > 0 && selectedAbnormalGroups.value.length < abnormalGroups.length;
  });

  // 获取所有异常项目组合
  function getAbnormalGroups() {
    const abnormalGroups = [];
    originDepartGroupList.value.forEach((departGroupBean) => {
      departGroupBean.groupList.forEach((group) => {
        // 检查是否有异常的小项
        const hasAbnormalItems = group.resultList && group.resultList.some(item => item.abnormalFlag === '1');
        if (hasAbnormalItems) {
          abnormalGroups.push({
            itemGroupId: group.itemGroupId,
            itemGroupName: group.itemGroupName,
            checkPartName: group.checkPartName,
            departName: departGroupBean.depart.departName
          });
        }
      });
    });
    return abnormalGroups;
  }

  /**结果详情部分*/
  const visible = ref(false);
  const resultDetailVisiable = ref(false);
  const currentRecord: ICustomerRegItemResult = ref({});
  function showDetail(record) {
    //根据record.itemGroupName，在departGroupList中找到对应的group，然后取group的reportPics
    let reportPics = [];
    for (const depart of departGroupList.value) {
      for (const group of depart.groupList) {
        if (group.itemGroupName === record.itemGroupName) {
          reportPics = group.reportPics || [];
          break;
        }
      }
      if (reportPics.length > 0) break;
    }
    currentRecord.value = { ...record, pic: reportPics };
    resultDetailVisiable.value = true;
  }
  function closeDetail() {
    resultDetailVisiable.value = false;
  }
  /**项目组合部分*/
  const originDepartGroupList = ref([]);
  const departGroupList = computed(() => {
    return originDepartGroupList.value.map((departGroupBean) => {
      const filteredGroupList = departGroupBean.groupList.map((group) => {
        let newGroup = { ...group, resultList: [...group.resultList] };
        if (resultCondition.value === 'abnormal') {
          newGroup.resultList = newGroup.resultList.filter((item) => item.abnormalFlag && item.abnormalFlag != '0');
        } else if (resultCondition.value === 'uncheck') {
          newGroup.resultList = newGroup.resultList.filter((item) => item.checkStatus === '未检');
        } else if (resultCondition.value === 'abandon') {
          newGroup.resultList = newGroup.resultList.filter((item) => item.abandonFlag == '1');
        }
        return newGroup;
      });

      return {
        departSummary: departGroupBean.departSummary,
        depart: departGroupBean.depart,
        groupList: filteredGroupList,
      };
    });
  });

  const filteredResultList = (condition) => {
    let resultList = [];
    originDepartGroupList.value.forEach((departGroupBean) => {
      departGroupBean.groupList.forEach((group) => {
        if (condition === 'abnormal') {
          resultList.push(...group.resultList.filter((item) => item.abnormalFlag && item.abnormalFlag != '0'));
        } else if (condition === 'uncheck') {
          if (group.checkStatus === '未检') {
            resultList.push(group);
          }
        } else if (condition === 'abandon') {
          if (group.abandonFlag == '1') {
            resultList.push(group);
          }
        }
      });
    });
    return resultList;
  };

  //统计出结果为异常的项目数量和未检的项目数量
  const statistics = computed(() => {
    let abnormalCount = 0;
    let uncheckCount = 0;
    let abandonCount = 0;
    originDepartGroupList.value?.forEach((departGroupBean) => {
      departGroupBean.groupList.forEach((group) => {
        group.resultList.forEach((item) => {
          if (item.abnormalFlag && item.abnormalFlag != '0') {
            abnormalCount++;
          }
        });
        // if (group.abnormalFlag && group.abnormalFlag != '0') {
        //   abnormalCount++;
        // }
        if (group.checkStatus == '未检') {
          uncheckCount++;
        }
        if (group.abandonFlag == '1') {
          abandonCount++;
        }
      });
    });
    return { abnormalCount, uncheckCount, abandonCount };
  });

  function loadData(regId, tab) {
    customerRegId.value = regId;
    groupLoading.value = true;
    // 重置选择状态
    selectedAbnormalGroups.value = [];

    let reqParam = { regId: customerRegId.value };

    listDepartGroupByReg(reqParam)
      .then((res) => {
        originDepartGroupList.value = res;
        resultCondition.value = tab || 'all';
        emit('loadData', res);
      })
      .finally(() => {
        groupLoading.value = false;
      });
  }

  const priewPic = (urls) => {
    console.log('预览图片的urls', urls);
    preview({
      images: urls.map((item) => getFileAccessHttpUrl(item)),
    });
  };

  // 处理异常项目组合选择
  function handleAbnormalGroupSelect(groupId, checked) {
    if (checked) {
      if (!selectedAbnormalGroups.value.includes(groupId)) {
        selectedAbnormalGroups.value.push(groupId);
      }
    } else {
      const index = selectedAbnormalGroups.value.indexOf(groupId);
      if (index > -1) {
        selectedAbnormalGroups.value.splice(index, 1);
      }
    }
    // 通知父组件选中的异常项目发生变化
    emitSelectedAbnormalGroups();
  }

  // 处理全选异常项目
  function handleSelectAllAbnormal(e) {
    const abnormalGroups = getAbnormalGroups();
    if (e.target.checked) {
      selectedAbnormalGroups.value = abnormalGroups.map(group => group.itemGroupId);
    } else {
      selectedAbnormalGroups.value = [];
    }
    emitSelectedAbnormalGroups();
  }

  // 处理设置为复查项目
  function handleSetRecheck() {
    if (selectedAbnormalGroups.value.length === 0) {
      return;
    }

    const abnormalGroups = getAbnormalGroups();
    const selectedGroups = abnormalGroups.filter(group =>
      selectedAbnormalGroups.value.includes(group.itemGroupId)
    );

    // 通知父组件设置复查项目
    emit('abnormalGroupsSelected', selectedGroups);

    // 显示成功提示
    const { createMessage } = useMessage();
    createMessage.success(`已选择 ${selectedGroups.length} 个异常项目用于复查`);

    // 清空选择
    selectedAbnormalGroups.value = [];
  }

  // 向父组件发送选中的异常项目
  function emitSelectedAbnormalGroups() {
    const abnormalGroups = getAbnormalGroups();
    const selectedGroups = abnormalGroups.filter(group =>
      selectedAbnormalGroups.value.includes(group.itemGroupId)
    );
    emit('abnormalGroupsSelected', selectedGroups);
  }

  defineExpose({
    loadData,
    getAbnormalGroups,
    selectedAbnormalGroups,
  });
</script>

<style lang="less" scoped>
  .full-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }

    .ant-modal-body {
      flex: 1;
    }
  }
  .abandon {
    text-decoration: line-through #ff4d4f;
  }

  .error-modal {
    max-height: 50vh;
    overflow-y: scroll;
  }

  .chosenClass {
    opacity: 1;
    border-style: solid;
    border-width: 1px;
    border-color: v-bind('token.colorPrimary');
  }
  :deep .ant-collapse-content-box {
    padding: 2px !important;
  }
  .depart-summary {
    background-color: v-bind('token.colorPrimaryBg');
    border-radius: v-bind('token.borderRadius');
  }
  .icon-color {
    color: v-bind('token.colorPrimary');
  }
</style>
