import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/basicinfo/company/list',
  queryById = '/basicinfo/company/queryById',
  search = '/basicinfo/company/search',
  save = '/basicinfo/company/add',
  edit = '/basicinfo/company/edit',
  deleteOne = '/basicinfo/company/delete',
  deleteBatch = '/basicinfo/company/deleteBatch',
  importExcel = '/basicinfo/company/importExcel',
  exportXls = '/basicinfo/company/exportXls',
  autoComplete = '/basicinfo/company/autoComplete',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) => {
  return defHttp.get({ url: Api.list, params });
};

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 导入
 * @param params
 */
export const importExcel = (params) => {
  return defHttp.uploadFile({ url: Api.importExcel }, params);
};

/**
 * 导出
 * @param params
 */
export const exportXls = (params) => {
  return defHttp.get({ url: Api.exportXls, params }, { responseType: 'blob' });
};

/**
 * 根据ID查询
 * @param id
 */
export const queryById = (id) => {
  return defHttp.get({ url: Api.queryById, params: { id } });
};

/**
 * 搜索公司
 * @param keyword
 * @param onlyRootCompanies
 */
export const searchCompanyByKeyword = (keyword: string, onlyRootCompanies: boolean = true) => {
  const params: any = {
    pageNo: 1,
    pageSize: 50,
  };

  if (keyword && keyword.trim()) {
    params.name_LIKE = keyword.trim();
    params.helpChar_LIKE = keyword.trim();
    params._searchMode = 'OR';
  }

  if (onlyRootCompanies) {
    params.pid_NULL = true;
  }

  return defHttp.get({ url: Api.list, params });
};

/**
 * 单位自动完成接口（带缓存）
 * @param keyword 搜索关键词
 * @param pageSize 返回数量限制，默认50
 */
export const getCompanyAutoComplete = async (keyword?: string, pageSize: number = 50) => {
  const params: any = {
    pageSize,
  };

  if (keyword && keyword.trim()) {
    params.keyword = keyword.trim();
  }

  try {
    // 首先尝试使用带缓存的自动完成接口
    return await defHttp.get({ url: Api.autoComplete, params }, { isTransformResponse: false });
  } catch (error: any) {
    // 如果缓存接口失败，降级到普通搜索接口
    console.warn('自动完成接口缓存错误，降级到普通搜索:', error?.message || error);

    // 使用普通搜索接口作为降级方案
    const searchParams: any = {
      pageNo: 1,
      pageSize,
    };

    if (keyword && keyword.trim()) {
      searchParams.name_LIKE = keyword.trim();
      searchParams.helpChar_LIKE = keyword.trim();
      searchParams._searchMode = 'OR';
    }

    // 只返回父级单位
    searchParams.pid_NULL = true;

    const fallbackResponse = await defHttp.get({ url: Api.list, params: searchParams });

    // 转换为自动完成接口的格式
    return {
      success: true,
      result: fallbackResponse.records || [],
      message: '数据加载成功（降级模式）',
      code: 200,
      timestamp: Date.now()
    };
  }
};
