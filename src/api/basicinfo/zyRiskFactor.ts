import { defHttp } from '/@/utils/http/axios';
import type {
  ZyRiskFactor,
  ZyRiskFactorAutoCompleteDTO,
  ZyRiskFactorAutoCompleteParams,
  ZyRiskFactorBatchValidationRequest,
  ZyRiskFactorBatchValidationResponse,
  ZyRiskFactorListResult,
  ZyRiskFactorSearchParams,
} from '/@/types/basicinfo/zyRiskFactor';

enum Api {
  list = '/basicinfo/zyRiskFactor/list',
  queryById = '/basicinfo/zyRiskFactor/queryById',
  search = '/basicinfo/zyRiskFactor/search',
  save = '/basicinfo/zyRiskFactor/add',
  edit = '/basicinfo/zyRiskFactor/edit',
  deleteOne = '/basicinfo/zyRiskFactor/delete',
  deleteBatch = '/basicinfo/zyRiskFactor/deleteBatch',
  importExcel = '/basicinfo/zyRiskFactor/importExcel',
  exportXls = '/basicinfo/zyRiskFactor/exportXls',
  autoComplete = '/basicinfo/zyRiskFactor/autoComplete',
  batchValidate = '/basicinfo/zyRiskFactor/batchValidate',
  exactMatch = '/basicinfo/zyRiskFactor/exactMatch',
  getByNames = '/basicinfo/zyRiskFactor/getByNames',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params: ZyRiskFactorSearchParams) => {
  return defHttp.get<ZyRiskFactorListResult>({ url: Api.list, params });
};

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params: { id: string }, handleSuccess: () => void) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params: { ids: string }, handleSuccess: () => void) => {
  return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params: ZyRiskFactor, isUpdate: boolean) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 导入
 * @param params
 */
export const importExcel = (params: any) => {
  return defHttp.uploadFile({ url: Api.importExcel }, params);
};

/**
 * 导出
 * @param params
 */
export const exportXls = (params: ZyRiskFactorSearchParams) => {
  return defHttp.get({ url: Api.exportXls, params }, { responseType: 'blob' });
};

/**
 * 根据ID查询
 * @param id
 */
export const queryById = (id: string) => {
  return defHttp.get<ZyRiskFactor>({ url: Api.queryById, params: { id } });
};

/**
 * 搜索危害因素
 * @param keyword 搜索关键词
 * @param searchType 搜索类型
 */
export const searchZyRiskFactorByKeyword = (
  keyword: string,
  searchType: 'name' | 'code' | 'helpChar' | 'wubiChar' | 'all' = 'all'
) => {
  const params: ZyRiskFactorSearchParams = {
    pageNo: 1,
    pageSize: 50,
    _searchMode: 'OR',
  };

  if (keyword && keyword.trim()) {
    const trimmedKeyword = keyword.trim();
    
    switch (searchType) {
      case 'name':
        params.name_LIKE = trimmedKeyword;
        break;
      case 'code':
        params.code_LIKE = trimmedKeyword;
        break;
      case 'helpChar':
        params.helpChar_LIKE = trimmedKeyword;
        break;
      case 'wubiChar':
        params.wubiChar_LIKE = trimmedKeyword;
        break;
      case 'all':
      default:
        params.name_LIKE = trimmedKeyword;
        params.code_LIKE = trimmedKeyword;
        params.helpChar_LIKE = trimmedKeyword;
        params.wubiChar_LIKE = trimmedKeyword;
        break;
    }
  }

  return defHttp.get<ZyRiskFactorListResult>({ url: Api.list, params });
};

/**
 * 危害因素自动完成接口（带缓存）
 * @param keyword 搜索关键词
 * @param pageSize 返回数量限制，默认50
 * @param searchType 搜索类型
 */
export const getZyRiskFactorAutoComplete = async (
  keyword?: string,
  pageSize: number = 50,
  searchType: 'name' | 'code' | 'helpChar' | 'wubiChar' | 'all' = 'all'
) => {
  const params: ZyRiskFactorAutoCompleteParams = {
    pageSize,
    searchType,
  };

  if (keyword && keyword.trim()) {
    params.keyword = keyword.trim();
  }

  try {
    // 首先尝试使用带缓存的自动完成接口
    return await defHttp.get<{
      success: boolean;
      result: ZyRiskFactorAutoCompleteDTO[];
      message: string;
      code: number;
      timestamp: number;
    }>({ url: Api.autoComplete, params }, { isTransformResponse: false });
  } catch (error: any) {
    // 如果缓存接口失败，降级到普通搜索接口
    console.warn('危害因素自动完成接口缓存错误，降级到普通搜索:', error?.message || error);

    const fallbackResponse = await searchZyRiskFactorByKeyword(keyword || '', searchType);

    // 转换为自动完成接口的格式
    return {
      success: true,
      result: fallbackResponse.records?.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code,
        helpChar: item.helpChar,
        wubiChar: item.wubiChar,
        type: item.type,
        category: item.category,
        harmLevel: item.harmLevel,
        sortNo: item.sortNo,
      })) || [],
      message: '数据加载成功（降级模式）',
      code: 200,
      timestamp: Date.now()
    };
  }
};

/**
 * 批量验证危害因素名称
 * @param names 危害因素名称列表
 */
export const batchValidateZyRiskFactorNames = async (names: string[]) => {
  const request: ZyRiskFactorBatchValidationRequest = { names };
  
  try {
    return await defHttp.post<ZyRiskFactorBatchValidationResponse>(
      { url: Api.batchValidate, data: request },
      { isTransformResponse: false }
    );
  } catch (error: any) {
    console.warn('批量验证接口失败，使用降级方案:', error?.message || error);
    
    // 降级方案：逐个查询精确匹配
    const results = await Promise.all(
      names.map(async (name) => {
        try {
          const response = await exactMatchZyRiskFactorByName(name);
          return {
            inputName: name,
            found: response.success && !!response.result,
            matchedFactor: response.result,
            errorMessage: response.success ? undefined : '未找到匹配的危害因素'
          };
        } catch (err) {
          return {
            inputName: name,
            found: false,
            errorMessage: '查询失败'
          };
        }
      })
    );

    const successCount = results.filter(r => r.found).length;
    const matchedFactors = results.filter(r => r.matchedFactor).map(r => r.matchedFactor!);

    return {
      success: true,
      result: {
        results,
        successCount,
        failureCount: names.length - successCount,
        matchedFactors
      },
      message: '验证完成（降级模式）',
      code: 200,
      timestamp: Date.now()
    };
  }
};

/**
 * 精确匹配危害因素名称
 * @param name 危害因素名称
 */
export const exactMatchZyRiskFactorByName = async (name: string) => {
  return await defHttp.get<{
    success: boolean;
    result?: ZyRiskFactorAutoCompleteDTO;
    message: string;
    code: number;
  }>({ url: Api.exactMatch, params: { name } }, { isTransformResponse: false });
};

/**
 * 根据名称列表获取危害因素
 * @param names 危害因素名称列表
 */
export const getZyRiskFactorsByNames = async (names: string[]) => {
  return await defHttp.post<{
    success: boolean;
    result: ZyRiskFactorAutoCompleteDTO[];
    message: string;
    code: number;
  }>({ url: Api.getByNames, data: { names } }, { isTransformResponse: false });
};

/**
 * 解析多值输入
 * @param input 输入字符串
 * @param separator 分隔符，默认为空格
 */
export const parseMultiValueInput = (input: string, separator: string = ' ') => {
  if (!input || !input.trim()) {
    return {
      rawInput: input,
      names: [],
      validCount: 0
    };
  }

  // 使用分隔符分割，并过滤空字符串
  const names = input
    .split(separator)
    .map(name => name.trim())
    .filter(name => name.length > 0);

  return {
    rawInput: input,
    names,
    validCount: names.length
  };
};
