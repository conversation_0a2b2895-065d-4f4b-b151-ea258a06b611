import { useMessage } from '/@/hooks/web/useMessage';
import {
  batchValidateZyRiskFactorNames,
  exactMatchZyRiskFactorByName,
  getZyRiskFactorsByNames,
} from '/@/api/basicinfo/zyRiskFactor';
import {
  parseMultiValueInputAdvanced,
  batchValidateRiskFactorNames,
  deduplicateRiskFactorNames,
} from '/@/utils/zyRiskFactorUtils';
import type {
  ZyRiskFactorValidationResult,
  ZyRiskFactorBatchValidationResponse,
  ZyRiskFactorAutoCompleteDTO,
} from '/@/types/basicinfo/zyRiskFactor';

/**
 * 危害因素验证服务
 */
export class ZyRiskFactorValidationService {
  private message = useMessage();

  /**
   * 验证单个危害因素名称
   * @param name 危害因素名称
   * @param showMessage 是否显示消息提示
   */
  async validateSingle(name: string, showMessage: boolean = true): Promise<ZyRiskFactorValidationResult> {
    try {
      if (!name || !name.trim()) {
        const result: ZyRiskFactorValidationResult = {
          inputName: name,
          found: false,
          errorMessage: '名称不能为空'
        };
        
        if (showMessage) {
          this.message.createError('危害因素名称不能为空');
        }
        
        return result;
      }

      const trimmedName = name.trim();
      const response = await exactMatchZyRiskFactorByName(trimmedName);

      const result: ZyRiskFactorValidationResult = {
        inputName: trimmedName,
        found: response.success && !!response.result,
        matchedFactor: response.result,
        errorMessage: response.success ? undefined : response.message
      };

      if (showMessage) {
        if (result.found) {
          this.message.createSuccess(`危害因素"${trimmedName}"验证成功`);
        } else {
          this.message.createError(`未找到危害因素"${trimmedName}"`);
        }
      }

      return result;
    } catch (error: any) {
      console.error('验证单个危害因素失败:', error);
      
      const result: ZyRiskFactorValidationResult = {
        inputName: name,
        found: false,
        errorMessage: '验证失败，请重试'
      };

      if (showMessage) {
        this.message.createError('验证失败，请重试');
      }

      return result;
    }
  }

  /**
   * 批量验证危害因素名称
   * @param names 危害因素名称列表
   * @param showMessage 是否显示消息提示
   * @param deduplication 是否去重
   */
  async validateBatch(
    names: string[],
    showMessage: boolean = true,
    deduplication: boolean = true
  ): Promise<ZyRiskFactorBatchValidationResponse> {
    try {
      if (!names || names.length === 0) {
        const response: ZyRiskFactorBatchValidationResponse = {
          results: [],
          successCount: 0,
          failureCount: 0,
          matchedFactors: []
        };

        if (showMessage) {
          this.message.createWarning('没有需要验证的危害因素');
        }

        return response;
      }

      // 预处理名称列表
      let processedNames = names.map(name => name.trim()).filter(name => name.length > 0);
      
      if (deduplication) {
        processedNames = deduplicateRiskFactorNames(processedNames);
      }

      // 格式验证
      const formatValidation = batchValidateRiskFactorNames(processedNames);
      
      if (formatValidation.invalidCount > 0 && showMessage) {
        const invalidMessages = formatValidation.invalidNames.map(
          item => `"${item.name}": ${item.message}`
        ).join(', ');
        this.message.createWarning(`格式错误的名称: ${invalidMessages}`);
      }

      // 只验证格式正确的名称
      if (formatValidation.validCount === 0) {
        const response: ZyRiskFactorBatchValidationResponse = {
          results: formatValidation.invalidNames.map(item => ({
            inputName: item.name,
            found: false,
            errorMessage: item.message
          })),
          successCount: 0,
          failureCount: formatValidation.invalidCount,
          matchedFactors: []
        };

        if (showMessage) {
          this.message.createError('所有名称格式都不正确');
        }

        return response;
      }

      // 调用后端批量验证接口
      const apiResponse = await batchValidateZyRiskFactorNames(formatValidation.validNames);

      if (!apiResponse.success) {
        throw new Error(apiResponse.message || '批量验证失败');
      }

      // 合并格式验证失败的结果
      const allResults = [
        ...apiResponse.result.results,
        ...formatValidation.invalidNames.map(item => ({
          inputName: item.name,
          found: false,
          errorMessage: item.message
        }))
      ];

      const finalResponse: ZyRiskFactorBatchValidationResponse = {
        results: allResults,
        successCount: apiResponse.result.successCount,
        failureCount: apiResponse.result.failureCount + formatValidation.invalidCount,
        matchedFactors: apiResponse.result.matchedFactors
      };

      if (showMessage) {
        const { successCount, failureCount } = finalResponse;
        const totalCount = successCount + failureCount;
        
        if (successCount === totalCount) {
          this.message.createSuccess(`所有${totalCount}个危害因素验证成功`);
        } else if (successCount > 0) {
          this.message.createWarning(`验证完成：${successCount}个成功，${failureCount}个失败`);
        } else {
          this.message.createError(`所有${totalCount}个危害因素验证失败`);
        }
      }

      return finalResponse;
    } catch (error: any) {
      console.error('批量验证危害因素失败:', error);
      
      const response: ZyRiskFactorBatchValidationResponse = {
        results: names.map(name => ({
          inputName: name,
          found: false,
          errorMessage: '验证失败'
        })),
        successCount: 0,
        failureCount: names.length,
        matchedFactors: []
      };

      if (showMessage) {
        this.message.createError('批量验证失败，请重试');
      }

      return response;
    }
  }

  /**
   * 验证多值输入字符串
   * @param input 输入字符串
   * @param separator 分隔符
   * @param showMessage 是否显示消息提示
   */
  async validateMultiValueInput(
    input: string,
    separator: string = ' ',
    showMessage: boolean = true
  ): Promise<{
    parseResult: ReturnType<typeof parseMultiValueInputAdvanced>;
    validationResponse: ZyRiskFactorBatchValidationResponse;
  }> {
    // 解析输入
    const parseResult = parseMultiValueInputAdvanced(input, [separator]);
    
    if (parseResult.validCount === 0) {
      const validationResponse: ZyRiskFactorBatchValidationResponse = {
        results: [],
        successCount: 0,
        failureCount: 0,
        matchedFactors: []
      };

      if (showMessage) {
        this.message.createWarning('没有解析到有效的危害因素名称');
      }

      return { parseResult, validationResponse };
    }

    // 批量验证
    const validationResponse = await this.validateBatch(parseResult.names, showMessage);

    return { parseResult, validationResponse };
  }

  /**
   * 获取验证结果摘要
   * @param results 验证结果列表
   */
  getValidationSummary(results: ZyRiskFactorValidationResult[]): {
    total: number;
    success: number;
    failure: number;
    successRate: number;
    successNames: string[];
    failureNames: string[];
    matchedFactors: ZyRiskFactorAutoCompleteDTO[];
  } {
    const total = results.length;
    const successResults = results.filter(r => r.found);
    const failureResults = results.filter(r => !r.found);
    
    return {
      total,
      success: successResults.length,
      failure: failureResults.length,
      successRate: total > 0 ? (successResults.length / total) * 100 : 0,
      successNames: successResults.map(r => r.inputName),
      failureNames: failureResults.map(r => r.inputName),
      matchedFactors: successResults.map(r => r.matchedFactor!).filter(Boolean)
    };
  }

  /**
   * 导出验证结果为CSV格式
   * @param results 验证结果列表
   */
  exportValidationResultsToCSV(results: ZyRiskFactorValidationResult[]): string {
    const headers = ['输入名称', '验证结果', '匹配名称', '匹配编码', '错误信息'];
    const rows = results.map(result => [
      result.inputName,
      result.found ? '成功' : '失败',
      result.matchedFactor?.name || '',
      result.matchedFactor?.code || '',
      result.errorMessage || ''
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    return csvContent;
  }

  /**
   * 下载验证结果CSV文件
   * @param results 验证结果列表
   * @param filename 文件名
   */
  downloadValidationResultsCSV(results: ZyRiskFactorValidationResult[], filename?: string): void {
    const csvContent = this.exportValidationResultsToCSV(results);
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename || `危害因素验证结果_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }
}

// 创建单例实例
export const zyRiskFactorValidationService = new ZyRiskFactorValidationService();
