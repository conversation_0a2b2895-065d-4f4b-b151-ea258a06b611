/**
 * 危害因素自动完成组件样式主题
 */

// 主题变量
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #fa8c16;
@error-color: #ff4d4f;
@info-color: #1890ff;

@border-radius-base: 6px;
@border-radius-sm: 4px;
@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

// 组件主容器
.zy-risk-factor-autocomplete {
  position: relative;
  
  // 自动完成输入框样式增强
  .ant-select {
    .ant-select-selector {
      border-radius: @border-radius-base;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      
      &:hover {
        border-color: @primary-color;
        box-shadow: 0 0 0 2px fade(@primary-color, 20%);
      }
      
      &:focus,
      &.ant-select-focused {
        border-color: @primary-color;
        box-shadow: 0 0 0 2px fade(@primary-color, 20%);
      }
    }
  }

  // 选项样式增强
  .risk-factor-option {
    padding: 8px 12px;
    border-radius: @border-radius-sm;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: fade(@primary-color, 8%);
    }

    .risk-factor-name {
      font-size: 14px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: 1.5;
      margin-bottom: 4px;
    }

    .risk-factor-details {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      align-items: center;

      .risk-factor-code {
        font-size: 11px;
        color: @primary-color;
        background: fade(@primary-color, 10%);
        padding: 2px 6px;
        border-radius: @border-radius-sm;
        border: 1px solid fade(@primary-color, 20%);
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }

      .risk-factor-help-char {
        font-size: 11px;
        color: @success-color;
        background: fade(@success-color, 10%);
        padding: 2px 6px;
        border-radius: @border-radius-sm;
        border: 1px solid fade(@success-color, 20%);
        font-weight: 500;
      }

      .risk-factor-wubi-char {
        font-size: 11px;
        color: #722ed1;
        background: fade(#722ed1, 10%);
        padding: 2px 6px;
        border-radius: @border-radius-sm;
        border: 1px solid fade(#722ed1, 20%);
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }

      .risk-factor-harm-level {
        font-size: 11px;
        color: @warning-color;
        background: fade(@warning-color, 10%);
        padding: 2px 6px;
        border-radius: @border-radius-sm;
        border: 1px solid fade(@warning-color, 20%);
        font-weight: 500;
      }
    }
  }

  // 多值选择标签样式
  .selected-factors {
    margin-top: 12px;
    padding: 8px;
    background: fade(@primary-color, 3%);
    border: 1px dashed fade(@primary-color, 30%);
    border-radius: @border-radius-base;
    min-height: 40px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: flex-start;
    transition: all 0.3s ease;

    &:empty {
      display: none;
    }

    .selected-factor-tag {
      display: inline-flex;
      align-items: center;
      max-width: 200px;
      background: #fff;
      border: 1px solid fade(@primary-color, 30%);
      border-radius: @border-radius-base;
      padding: 4px 8px;
      font-size: 12px;
      transition: all 0.2s ease;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

      &:hover {
        border-color: @primary-color;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
      }

      .factor-code {
        margin-left: 4px;
        font-size: 10px;
        color: rgba(0, 0, 0, 0.45);
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }

      .ant-tag-close-icon {
        margin-left: 6px;
        color: rgba(0, 0, 0, 0.45);
        transition: color 0.2s ease;

        &:hover {
          color: @error-color;
        }
      }
    }
  }

  // 验证结果样式
  .validation-results {
    margin-top: 12px;
    padding: 12px;
    background: #fafafa;
    border-radius: @border-radius-base;
    border: 1px solid #e8e8e8;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);

    .validation-item {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      padding: 4px 8px;
      border-radius: @border-radius-sm;
      font-size: 12px;
      transition: all 0.2s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &.success {
        color: @success-color;
        background: fade(@success-color, 8%);
        border: 1px solid fade(@success-color, 20%);

        .anticon {
          color: @success-color;
          margin-right: 6px;
        }
      }

      &.error {
        color: @error-color;
        background: fade(@error-color, 8%);
        border: 1px solid fade(@error-color, 20%);

        .anticon {
          color: @error-color;
          margin-right: 6px;
        }
      }

      .validation-text {
        flex: 1;
        font-weight: 500;
      }
    }
  }

  // 加载状态样式
  &.loading {
    .ant-select-selector {
      background: linear-gradient(90deg, #f0f0f0 25%, transparent 37%, #f0f0f0 63%);
      background-size: 400% 100%;
      animation: loading 1.4s ease infinite;
    }
  }

  // 错误状态样式
  &.error {
    .ant-select-selector {
      border-color: @error-color;
      box-shadow: 0 0 0 2px fade(@error-color, 20%);
    }
  }

  // 成功状态样式
  &.success {
    .ant-select-selector {
      border-color: @success-color;
      box-shadow: 0 0 0 2px fade(@success-color, 20%);
    }
  }

  // 禁用状态样式
  &.disabled {
    .ant-select-selector {
      background-color: #f5f5f5;
      color: rgba(0, 0, 0, 0.25);
      cursor: not-allowed;
    }
  }
}

// 下拉面板样式增强
.ant-select-dropdown {
  .ant-select-item {
    border-radius: @border-radius-sm;
    margin: 2px 4px;
    transition: all 0.2s ease;

    &.ant-select-item-option-selected {
      background-color: fade(@primary-color, 10%);
      border: 1px solid fade(@primary-color, 30%);
    }

    &.ant-select-item-option-active {
      background-color: fade(@primary-color, 8%);
    }

    .ant-select-item-option-content {
      white-space: normal;
      word-break: break-word;
    }
  }

  // 空状态样式
  .ant-empty {
    padding: 20px;
    
    .ant-empty-description {
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
    }
  }

  // 加载状态样式
  .ant-spin {
    padding: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .zy-risk-factor-autocomplete {
    .selected-factors {
      .selected-factor-tag {
        max-width: 150px;
        font-size: 11px;
      }
    }

    .risk-factor-option {
      .risk-factor-details {
        gap: 4px;
        
        > span {
          font-size: 10px;
          padding: 1px 4px;
        }
      }
    }
  }
}

// 动画定义
@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 动画应用
.validation-results {
  animation: fadeIn 0.3s ease;
}

.selected-factor-tag {
  animation: slideIn 0.2s ease;
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .zy-risk-factor-autocomplete {
    .selected-factors {
      background: fade(#fff, 5%);
      border-color: fade(#fff, 20%);
    }

    .validation-results {
      background: #1f1f1f;
      border-color: #434343;
    }

    .selected-factor-tag {
      background: #2f2f2f;
      border-color: fade(#fff, 20%);
      color: rgba(255, 255, 255, 0.85);
    }
  }
}

// 高对比度主题支持
@media (prefers-contrast: high) {
  .zy-risk-factor-autocomplete {
    .risk-factor-option {
      .risk-factor-details > span {
        border-width: 2px;
        font-weight: 600;
      }
    }

    .selected-factor-tag {
      border-width: 2px;
      font-weight: 500;
    }
  }
}
