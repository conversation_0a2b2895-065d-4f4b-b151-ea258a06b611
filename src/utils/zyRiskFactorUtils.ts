import type { MultiValueParseResult } from '/@/types/basicinfo/zyRiskFactor';

/**
 * 危害因素工具函数
 */

/**
 * 解析多值输入的增强版本
 * 支持多种分隔符和智能识别
 * @param input 输入字符串
 * @param separators 分隔符数组，默认支持空格、逗号、分号、换行
 */
export function parseMultiValueInputAdvanced(
  input: string,
  separators: string[] = [' ', ',', '，', ';', '；', '\n', '\r\n']
): MultiValueParseResult {
  if (!input || !input.trim()) {
    return {
      rawInput: input,
      names: [],
      validCount: 0
    };
  }

  let processedInput = input.trim();
  
  // 创建正则表达式来匹配所有分隔符
  const separatorRegex = new RegExp(`[${separators.map(s => escapeRegExp(s)).join('')}]+`, 'g');
  
  // 使用正则表达式分割字符串
  const names = processedInput
    .split(separatorRegex)
    .map(name => name.trim())
    .filter(name => name.length > 0)
    .filter(name => name.length <= 100) // 限制单个名称长度
    .slice(0, 50); // 限制最大数量

  return {
    rawInput: input,
    names,
    validCount: names.length
  };
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 验证危害因素名称格式
 * @param name 危害因素名称
 */
export function validateRiskFactorName(name: string): {
  valid: boolean;
  message?: string;
} {
  if (!name || !name.trim()) {
    return { valid: false, message: '名称不能为空' };
  }

  const trimmedName = name.trim();

  // 长度检查
  if (trimmedName.length < 2) {
    return { valid: false, message: '名称长度不能少于2个字符' };
  }

  if (trimmedName.length > 50) {
    return { valid: false, message: '名称长度不能超过50个字符' };
  }

  // 字符检查 - 只允许中文、英文、数字、常见符号
  const validCharRegex = /^[\u4e00-\u9fa5a-zA-Z0-9\-_()（）\[\]【】\s\.·]+$/;
  if (!validCharRegex.test(trimmedName)) {
    return { valid: false, message: '名称包含不支持的字符' };
  }

  // 不允许纯数字
  if (/^\d+$/.test(trimmedName)) {
    return { valid: false, message: '名称不能为纯数字' };
  }

  return { valid: true };
}

/**
 * 批量验证危害因素名称格式
 * @param names 危害因素名称列表
 */
export function batchValidateRiskFactorNames(names: string[]): {
  validNames: string[];
  invalidNames: { name: string; message: string }[];
  validCount: number;
  invalidCount: number;
} {
  const validNames: string[] = [];
  const invalidNames: { name: string; message: string }[] = [];

  names.forEach(name => {
    const validation = validateRiskFactorName(name);
    if (validation.valid) {
      validNames.push(name.trim());
    } else {
      invalidNames.push({
        name: name,
        message: validation.message || '格式错误'
      });
    }
  });

  return {
    validNames,
    invalidNames,
    validCount: validNames.length,
    invalidCount: invalidNames.length
  };
}

/**
 * 去重危害因素名称
 * @param names 危害因素名称列表
 * @param caseSensitive 是否区分大小写，默认false
 */
export function deduplicateRiskFactorNames(names: string[], caseSensitive: boolean = false): string[] {
  const seen = new Set<string>();
  const result: string[] = [];

  names.forEach(name => {
    const key = caseSensitive ? name.trim() : name.trim().toLowerCase();
    if (!seen.has(key)) {
      seen.add(key);
      result.push(name.trim());
    }
  });

  return result;
}

/**
 * 格式化危害因素显示文本
 * @param factor 危害因素对象
 * @param showCode 是否显示编码
 * @param showHelpChar 是否显示助记码
 */
export function formatRiskFactorDisplay(
  factor: { name: string; code?: string; helpChar?: string },
  showCode: boolean = true,
  showHelpChar: boolean = true
): string {
  let display = factor.name;
  
  const details: string[] = [];
  
  if (showCode && factor.code) {
    details.push(`编码:${factor.code}`);
  }
  
  if (showHelpChar && factor.helpChar) {
    details.push(`助记:${factor.helpChar}`);
  }
  
  if (details.length > 0) {
    display += ` (${details.join(', ')})`;
  }
  
  return display;
}

/**
 * 智能匹配危害因素名称
 * 支持模糊匹配、拼音匹配等
 * @param input 输入文本
 * @param candidates 候选列表
 * @param threshold 匹配阈值，0-1之间
 */
export function smartMatchRiskFactors(
  input: string,
  candidates: { name: string; helpChar?: string; wubiChar?: string }[],
  threshold: number = 0.6
): { name: string; score: number }[] {
  if (!input || !input.trim()) {
    return [];
  }

  const inputLower = input.trim().toLowerCase();
  const results: { name: string; score: number }[] = [];

  candidates.forEach(candidate => {
    let maxScore = 0;
    
    // 精确匹配
    if (candidate.name.toLowerCase() === inputLower) {
      maxScore = 1.0;
    }
    // 包含匹配
    else if (candidate.name.toLowerCase().includes(inputLower)) {
      maxScore = 0.8;
    }
    // 助记码匹配
    else if (candidate.helpChar && candidate.helpChar.toLowerCase().includes(inputLower)) {
      maxScore = 0.7;
    }
    // 五笔码匹配
    else if (candidate.wubiChar && candidate.wubiChar.toLowerCase().includes(inputLower)) {
      maxScore = 0.6;
    }
    // 简单的字符相似度匹配
    else {
      maxScore = calculateStringSimilarity(inputLower, candidate.name.toLowerCase());
    }

    if (maxScore >= threshold) {
      results.push({
        name: candidate.name,
        score: maxScore
      });
    }
  });

  // 按分数降序排序
  return results.sort((a, b) => b.score - a.score);
}

/**
 * 计算字符串相似度（简单版本）
 * @param str1 字符串1
 * @param str2 字符串2
 */
function calculateStringSimilarity(str1: string, str2: string): number {
  if (str1 === str2) return 1.0;
  if (str1.length === 0 || str2.length === 0) return 0.0;

  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  const editDistance = calculateLevenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

/**
 * 计算编辑距离（Levenshtein距离）
 * @param str1 字符串1
 * @param str2 字符串2
 */
function calculateLevenshteinDistance(str1: string, str2: string): number {
  const matrix: number[][] = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1, // substitution
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j] + 1      // deletion
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
}
