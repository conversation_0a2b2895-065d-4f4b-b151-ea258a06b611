/**
 * 危害因素基础信息
 */
export interface ZyRiskFactor {
  /** 主键ID */
  id: string;
  /** 危害因素名称 */
  name: string;
  /** 危害因素编码 */
  code: string;
  /** 助记码 */
  helpChar?: string;
  /** 五笔码 */
  wubiChar?: string;
  /** 危害因素类型 */
  type?: string;
  /** 危害因素分类 */
  category?: string;
  /** 危害等级 */
  harmLevel?: string;
  /** 检测方法 */
  detectionMethod?: string;
  /** 职业接触限值 */
  occupationalLimit?: string;
  /** 健康影响 */
  healthEffect?: string;
  /** 防护措施 */
  protectiveMeasures?: string;
  /** 状态 */
  status?: string;
  /** 删除标志 */
  delFlag?: string;
  /** 备注 */
  remark?: string;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 排序号 */
  sortNo?: number;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 危害因素自动完成DTO
 */
export interface ZyRiskFactorAutoCompleteDTO {
  /** 危害因素ID */
  id: string;
  /** 危害因素名称 */
  name: string;
  /** 危害因素编码 */
  code: string;
  /** 助记码 */
  helpChar?: string;
  /** 五笔码 */
  wubiChar?: string;
  /** 危害因素类型 */
  type?: string;
  /** 危害因素分类 */
  category?: string;
  /** 危害等级 */
  harmLevel?: string;
  /** 排序号 */
  sortNo?: number;
}

/**
 * 自动完成选项（用于下拉选择）
 */
export interface ZyRiskFactorAutoCompleteOption {
  /** 值 */
  value: string;
  /** 标签 */
  label: string;
  /** 原始数据 */
  data: ZyRiskFactorAutoCompleteDTO;
}

/**
 * 危害因素验证结果
 */
export interface ZyRiskFactorValidationResult {
  /** 输入的名称 */
  inputName: string;
  /** 是否找到匹配 */
  found: boolean;
  /** 匹配的危害因素（如果找到） */
  matchedFactor?: ZyRiskFactorAutoCompleteDTO;
  /** 错误信息（如果未找到） */
  errorMessage?: string;
}

/**
 * 批量验证请求
 */
export interface ZyRiskFactorBatchValidationRequest {
  /** 危害因素名称列表 */
  names: string[];
}

/**
 * 批量验证响应
 */
export interface ZyRiskFactorBatchValidationResponse {
  /** 验证结果列表 */
  results: ZyRiskFactorValidationResult[];
  /** 成功匹配的数量 */
  successCount: number;
  /** 失败的数量 */
  failureCount: number;
  /** 匹配的危害因素列表 */
  matchedFactors: ZyRiskFactorAutoCompleteDTO[];
}

/**
 * API响应结果
 */
export interface ZyRiskFactorListResult {
  /** 记录列表 */
  records: ZyRiskFactor[];
  /** 总数 */
  total: number;
  /** 当前页 */
  current: number;
  /** 页大小 */
  size: number;
}

/**
 * 搜索参数
 */
export interface ZyRiskFactorSearchParams {
  /** 页码 */
  pageNo?: number;
  /** 页大小 */
  pageSize?: number;
  /** 危害因素名称（模糊查询） */
  name_LIKE?: string;
  /** 危害因素编码（模糊查询） */
  code_LIKE?: string;
  /** 助记码（模糊查询） */
  helpChar_LIKE?: string;
  /** 五笔码（模糊查询） */
  wubiChar_LIKE?: string;
  /** 危害因素类型 */
  type?: string;
  /** 危害因素分类 */
  category?: string;
  /** 搜索模式 */
  _searchMode?: 'AND' | 'OR';
  /** 状态 */
  status?: string;
  /** 删除标志 */
  delFlag?: string;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 自动完成搜索参数
 */
export interface ZyRiskFactorAutoCompleteParams {
  /** 搜索关键词 */
  keyword?: string;
  /** 返回数量限制 */
  pageSize?: number;
  /** 搜索类型：name-名称，code-编码，helpChar-助记码，wubiChar-五笔码，all-全部 */
  searchType?: 'name' | 'code' | 'helpChar' | 'wubiChar' | 'all';
}

/**
 * 多值输入解析结果
 */
export interface MultiValueParseResult {
  /** 原始输入 */
  rawInput: string;
  /** 解析出的名称列表 */
  names: string[];
  /** 有效名称数量 */
  validCount: number;
}

/**
 * 组件事件参数
 */
export interface ZyRiskFactorChangeEvent {
  /** 输入值 */
  value: string;
  /** 选中的危害因素（单选时） */
  selectedFactor?: ZyRiskFactorAutoCompleteDTO;
  /** 选中的危害因素列表（多选时） */
  selectedFactors?: ZyRiskFactorAutoCompleteDTO[];
  /** 是否为手动输入 */
  isManualInput: boolean;
  /** 验证结果（手动输入时） */
  validationResults?: ZyRiskFactorValidationResult[];
}

/**
 * 组件配置选项
 */
export interface ZyRiskFactorAutoCompleteConfig {
  /** 是否支持多值输入 */
  allowMultiple?: boolean;
  /** 多值分隔符 */
  separator?: string;
  /** 是否启用手动输入验证 */
  enableManualValidation?: boolean;
  /** 搜索防抖延迟（毫秒） */
  searchDebounce?: number;
  /** 最大显示选项数 */
  maxOptions?: number;
  /** 是否显示危害因素编码 */
  showCode?: boolean;
  /** 是否显示助记码 */
  showHelpChar?: boolean;
  /** 是否显示五笔码 */
  showWubiChar?: boolean;
  /** 是否显示危害等级 */
  showHarmLevel?: boolean;
}
